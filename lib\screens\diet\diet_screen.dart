import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_spacing.dart';
import '../../widgets/diet_app_bar.dart';
import '../../widgets/diet_tags_section.dart';
import '../../widgets/nutrition_pie_chart.dart';
import '../../widgets/chat_section.dart';
import '../../widgets/chat_input.dart';

class DietScreen extends StatelessWidget {
  const DietScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.gradientStart,
              AppColors.gradientEnd,
            ],
            stops: [0.0, 0.5431],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              const DietAppBar(),
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: AppSpacing.md),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: AppSpacing.md),
                      const DietTagsSection(),
                      SizedBox(height: AppSpacing.xl),
                      const NutritionPieChart(),
                      SizedBox(height: AppSpacing.xl),
                      const ChatSection(),
                      SizedBox(height: AppSpacing.lg),
                    ],
                  ),
                ),
              ),
              const ChatInput(),
            ],
          ),
        ),
      ),
    );
  }
}
