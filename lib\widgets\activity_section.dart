import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';
import 'activity_card.dart';

class ActivitySection extends StatelessWidget {
  const ActivitySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.refresh,
              color: AppColors.textPrimary,
              size: AppSpacing.iconMd,
            ),
            SizedBox(width: AppSpacing.sm),
            Text(
              'Activity',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        SizedBox(height: AppSpacing.md),
        Row(
          children: [
            Expanded(
              child: ActivityCard(
                icon: Icons.directions_walk,
                value: '8000',
                label: 'Steps',
              ),
            ),
            SizedBox(width: AppSpacing.md),
            Expanded(
              child: ActivityCard(
                icon: Icons.local_fire_department,
                value: '300',
                label: 'kCal',
              ),
            ),
            SizedBox(width: AppSpacing.md),
            Expanded(
              child: ActivityCard(
                icon: Icons.directions_walk,
                value: '8000',
                label: 'Steps',
              ),
            ),
          ],
        ),
      ],
    );
  }
}
