import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';

class RecoverySection extends StatelessWidget {
  const RecoverySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF2A4A6B),
            Color(0xFF1E3A5F),
          ],
        ),
        borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.bed_outlined,
                color: AppColors.textPrimary,
                size: AppSpacing.iconMd,
              ),
              SizedBox(width: AppSpacing.sm),
              Text(
                'Recovery',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.lg),
          Container(
            width: double.infinity,
            height: 40.h,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
            ),
            child: Stack(
              children: [
                Container(
                  width: (MediaQuery.of(context).size.width - 64.w) * 0.8,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: AppColors.recoveryProgress,
                    borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
                  ),
                ),
                Center(
                  child: Text(
                    '80%',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
