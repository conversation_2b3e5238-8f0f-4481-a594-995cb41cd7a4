import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';
import 'circular_progress_indicator_custom.dart';
import 'score_indicator.dart';

class BillScoreCard extends StatelessWidget {
  const BillScoreCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF2A4A6B),
            Color(0xFF1E3A5F),
          ],
        ),
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '126',
                    style: TextStyle(
                      fontSize: 48.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'Bill Score',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              const CircularProgressIndicatorCustom(
                score: 126,
                maxScore: 200,
              ),
            ],
          ),
          SizedBox(height: AppSpacing.lg),
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: ScoreIndicator(
                      color: AppColors.nourishment,
                      score: 20,
                      label: 'Nourishment',
                    ),
                  ),
                  SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: ScoreIndicator(
                      color: AppColors.movement,
                      score: 20,
                      label: 'Movement',
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppSpacing.md),
              Row(
                children: [
                  Expanded(
                    child: ScoreIndicator(
                      color: AppColors.rest,
                      score: 20,
                      label: 'Rest',
                    ),
                  ),
                  SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: ScoreIndicator(
                      color: AppColors.mindfulness,
                      score: 20,
                      label: 'Mindfulness',
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppSpacing.md),
              Row(
                children: [
                  Expanded(
                    child: ScoreIndicator(
                      color: AppColors.hydration,
                      score: 20,
                      label: 'Hydration',
                    ),
                  ),
                  const Expanded(child: SizedBox()),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
