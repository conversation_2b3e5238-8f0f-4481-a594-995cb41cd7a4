import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';
import 'tag_chip.dart';

class DailyTagsSection extends StatelessWidget {
  const DailyTagsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tag,
                color: AppColors.textPrimary,
                size: AppSpacing.iconMd,
              ),
              SizedBox(width: AppSpacing.sm),
              Text(
                'Daily Tags',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.md),
          Wrap(
            spacing: AppSpacing.sm,
            runSpacing: AppSpacing.sm,
            children: const [
              TagChip(label: 'Work Out'),
              TagChip(label: 'Heart Rate'),
              TagChip(label: 'Blood'),
              TagChip(label: 'Oxygen'),
              TagChip(label: 'HRV Stress'),
            ],
          ),
        ],
      ),
    );
  }
}
