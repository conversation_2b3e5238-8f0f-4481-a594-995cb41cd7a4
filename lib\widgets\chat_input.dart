import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';

class ChatInput extends StatelessWidget {
  const ChatInput({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.md,
                vertical: AppSpacing.sm,
              ),
              decoration: BoxDecoration(
                color: AppColors.inputBackground,
                borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textPrimary,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Type a message ...',
                        hintStyle: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondary,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                  SizedBox(width: AppSpacing.sm),
                  Icon(
                    Icons.camera_alt_outlined,
                    color: AppColors.textSecondary,
                    size: AppSpacing.iconSm,
                  ),
                  SizedBox(width: AppSpacing.sm),
                  Icon(
                    Icons.attach_file,
                    color: AppColors.textSecondary,
                    size: AppSpacing.iconSm,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: AppSpacing.sm),
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: AppColors.micButtonBackground,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.mic,
              color: AppColors.backgroundColor,
              size: AppSpacing.iconMd,
            ),
          ),
        ],
      ),
    );
  }
}
