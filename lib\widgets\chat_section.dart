import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';
import 'chat_bubble.dart';

class ChatSection extends StatelessWidget {
  const ChatSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ChatBubble(
          message: 'Hi!',
          time: '10:10',
          isUser: true,
        ),
        Sized<PERSON>ox(height: AppSpacing.md),
        ChatBubble(
          message: 'What are the best food?',
          time: '10:11',
          isUser: true,
        ),
        SizedBox(height: AppSpacing.md),
        ChatBubble(
          message: 'Offers a wide variety of incredible dishes that are loved both locally and globally. Here\'s a list of the best and most popular foods in Japan, across traditional and modern tastes',
          time: '10:11',
          isUser: false,
        ),
        SizedBox(height: AppSpacing.xl),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: AppSpacing.lg,
            vertical: AppSpacing.md,
          ),
          decoration: BoxDecoration(
            color: AppColors.primaryAccent,
            borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
          ),
          child: Text(
            'Talk to real life coach',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.backgroundColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
