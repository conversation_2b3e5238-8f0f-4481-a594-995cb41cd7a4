import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';

class CustomBottomNav extends StatelessWidget {
  final int currentIndex;

  const CustomBottomNav({super.key, this.currentIndex = 0});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80.h,
      decoration: BoxDecoration(
        color: AppColors.bottomNavBackground,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppSpacing.radiusLg),
          topRight: Radius.circular(AppSpacing.radiusLg),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(
            icon: Icons.home_outlined,
            label: 'Home',
            isSelected: currentIndex == 0,
          ),
          _buildNavItem(
            icon: Icons.psychology_outlined,
            label: 'AI Coach',
            isSelected: currentIndex == 1,
          ),
          _buildAddButton(),
          _buildNavItem(
            icon: Icons.emoji_events_outlined,
            label: 'Glow Points',
            isSelected: currentIndex == 3,
          ),
          _buildNavItem(
            icon: Icons.person_outline,
            label: 'Profile',
            isSelected: currentIndex == 4,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: isSelected
              ? AppColors.bottomNavSelected
              : AppColors.bottomNavUnselected,
          size: AppSpacing.iconMd,
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 10.sp,
            fontWeight: FontWeight.w500,
            color: isSelected
                ? AppColors.bottomNavSelected
                : AppColors.bottomNavUnselected,
          ),
        ),
      ],
    );
  }

  Widget _buildAddButton() {
    return Container(
      width: 56.w,
      height: 56.w,
      decoration: BoxDecoration(
        color: AppColors.addButtonBackground,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.add,
        color: AppColors.backgroundColor,
        size: AppSpacing.iconLg,
      ),
    );
  }
}
