import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';

class ProfileMenuItem extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;

  const ProfileMenuItem({
    super.key,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 60.h,
        decoration: BoxDecoration(
          color: AppColors.profileMenuItemBackground,
          borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
        ),
        child: Row(
          children: [
            SizedBox(width: AppSpacing.md),
            
            // Icon container
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColors.profileMenuIconBackground,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: AppColors.textPrimary,
              ),
            ),
            
            // Spacer to push content to edges
            const Spacer(),
            
            SizedBox(width: AppSpacing.md),
          ],
        ),
      ),
    );
  }
}
