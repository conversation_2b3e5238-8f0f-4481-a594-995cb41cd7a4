import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';

class DietTagChip extends StatelessWidget {
  final String label;
  final bool isSelected;

  const DietTagChip({
    super.key,
    required this.label,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: isSelected 
            ? AppColors.primaryAccent 
            : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
        border: isSelected 
            ? null 
            : Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          color: isSelected 
              ? AppColors.backgroundColor 
              : AppColors.textPrimary,
        ),
      ),
    );
  }
}
