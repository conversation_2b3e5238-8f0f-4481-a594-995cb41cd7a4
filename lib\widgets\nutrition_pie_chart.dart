import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';
import 'dart:math' as math;

class NutritionPieChart extends StatelessWidget {
  const NutritionPieChart({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 280.w,
        height: 280.w,
        child: CustomPaint(
          painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Legend
                _buildLegendItem('Veggies', '50%', AppColors.pieChartVeggies),
                SizedBox(height: 8.h),
                _buildLegendItem('Protein', '25%', AppColors.pieChartProtein),
                SizedBox(height: 8.h),
                _buildLegendItem('Carbs', '25%', AppColors.pieChartCarbs),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, String percentage, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppSpacing.radiusXs),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: label == 'Protein' ? AppColors.textPrimary : AppColors.backgroundColor,
            ),
          ),
          Text(
            percentage,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: label == 'Protein' ? AppColors.textPrimary : AppColors.backgroundColor,
            ),
          ),
        ],
      ),
    );
  }
}

class PieChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Veggies - 50% (180 degrees)
    final veggiesPaint = Paint()
      ..color = AppColors.pieChartVeggies
      ..style = PaintingStyle.fill;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start from top
      math.pi, // 180 degrees
      true,
      veggiesPaint,
    );

    // Protein - 25% (90 degrees)
    final proteinPaint = Paint()
      ..color = AppColors.pieChartProtein
      ..style = PaintingStyle.fill;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi / 2, // Start from bottom
      math.pi / 2, // 90 degrees
      true,
      proteinPaint,
    );

    // Carbs - 25% (90 degrees)
    final carbsPaint = Paint()
      ..color = AppColors.pieChartCarbs
      ..style = PaintingStyle.fill;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from left
      math.pi / 2, // 90 degrees
      true,
      carbsPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
