import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_spacing.dart';
import 'diet_tag_chip.dart';

class DietTagsSection extends StatelessWidget {
  const DietTagsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: AppSpacing.sm,
      runSpacing: AppSpacing.sm,
      children: const [
        DietTagChip(
          label: 'Check out recipe',
          isSelected: true,
        ),
        DietTagChip(
          label: 'Diet',
          isSelected: false,
        ),
        DietTagChip(
          label: 'Fitness',
          isSelected: false,
        ),
      ],
    );
  }
}
