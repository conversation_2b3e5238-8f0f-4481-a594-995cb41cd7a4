import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppSpacing {
  // Padding values
  static double get xs => 4.0.w;
  static double get sm => 8.0.w;
  static double get md => 16.0.w;
  static double get lg => 24.0.w;
  static double get xl => 32.0.w;
  static double get xxl => 48.0.w;
  
  // Margin values
  static double get marginXs => 4.0.w;
  static double get marginSm => 8.0.w;
  static double get marginMd => 16.0.w;
  static double get marginLg => 24.0.w;
  static double get marginXl => 32.0.w;
  
  // Border radius
  static double get radiusXs => 4.0.r;
  static double get radiusSm => 8.0.r;
  static double get radiusMd => 12.0.r;
  static double get radiusLg => 16.0.r;
  static double get radiusXl => 24.0.r;
  static double get radiusCircle => 50.0.r;
  
  // Icon sizes
  static double get iconXs => 16.0.w;
  static double get iconSm => 20.0.w;
  static double get iconMd => 24.0.w;
  static double get iconLg => 32.0.w;
  static double get iconXl => 48.0.w;
}
