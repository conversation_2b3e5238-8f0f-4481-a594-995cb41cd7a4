import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_spacing.dart';
import '../../widgets/custom_bottom_nav.dart';

class RoutineScreen extends StatefulWidget {
  const RoutineScreen({super.key});

  @override
  State<RoutineScreen> createState() => _RoutineScreenState();
}

class _RoutineScreenState extends State<RoutineScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.gradientStart, AppColors.gradientEnd],
            stops: [0.0, 0.5431],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header Section
              _buildHeader(),

              // Scrollable Content
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20.h),

                      // Calendar Week View
                      _buildCalendarWeekView(),

                      SizedBox(height: 24.h),

                      // Time Schedule Grid
                      _buildTimeScheduleGrid(),

                      SizedBox(height: 32.h),

                      // Activity Icons Section
                      _buildActivityIconsSection(),

                      SizedBox(height: 32.h),

                      // Create Routine Button
                      _buildCreateRoutineButton(),

                      SizedBox(height: 32.h),

                      // Checkout Routine Section
                      _buildCheckoutRoutineSection(),

                      SizedBox(height: 24.h),

                      // Promotional Card
                      _buildPromotionalCard(),

                      SizedBox(height: 100.h), // Space for bottom nav
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const CustomBottomNav(currentIndex: 0),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Status Bar
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '9:41',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Row(
                children: [
                  // Signal bars
                  Row(
                    children: List.generate(4, (index) {
                      return Container(
                        margin: EdgeInsets.only(right: 2.w),
                        width: 3.w,
                        height: (6 + index * 2).h,
                        decoration: BoxDecoration(
                          color: AppColors.textPrimary,
                          borderRadius: BorderRadius.circular(1.r),
                        ),
                      );
                    }),
                  ),
                  SizedBox(width: 4.w),
                  // WiFi icon
                  Icon(Icons.wifi, color: AppColors.textPrimary, size: 16.sp),
                  SizedBox(width: 4.w),
                  // Battery icon
                  Container(
                    width: 24.w,
                    height: 12.h,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.textPrimary,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(2.r),
                    ),
                    child: Stack(
                      children: [
                        Container(
                          margin: EdgeInsets.all(1.w),
                          width: 18.w,
                          decoration: BoxDecoration(
                            color: AppColors.textPrimary,
                            borderRadius: BorderRadius.circular(1.r),
                          ),
                        ),
                        Positioned(
                          right: -2.w,
                          top: 3.h,
                          child: Container(
                            width: 2.w,
                            height: 6.h,
                            decoration: BoxDecoration(
                              color: AppColors.textPrimary,
                              borderRadius: BorderRadius.circular(1.r),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Navigation Header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textPrimary,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 16.w),
              Text(
                'Today',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarWeekView() {
    final List<Map<String, dynamic>> weekDays = [
      {'day': '5', 'name': 'Sun', 'isSelected': false},
      {'day': '6', 'name': 'Mon', 'isSelected': false},
      {'day': '7', 'name': 'Tue', 'isSelected': false},
      {
        'day': '8',
        'name': 'Wed',
        'isSelected': true,
        'fullDate': '8 Jul\nWednesday',
      },
      {'day': '9', 'name': 'Thu', 'isSelected': false},
      {'day': '10', 'name': 'Fri', 'isSelected': false},
      {'day': '11', 'name': 'Sat', 'isSelected': false},
    ];

    return SizedBox(
      height: 80.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: weekDays.map((dayData) {
          final bool isSelected = dayData['isSelected'] as bool;
          return GestureDetector(
            onTap: () {
              // Handle day selection
            },
            child: Container(
              width: 45.w,
              height: 70.h,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primaryAccent
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isSelected && dayData['fullDate'] != null) ...[
                    Text(
                      dayData['fullDate'] as String,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.backgroundColor,
                        height: 1.2,
                      ),
                    ),
                  ] else ...[
                    Text(
                      dayData['day'] as String,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? AppColors.backgroundColor
                            : AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      dayData['name'] as String,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        color: isSelected
                            ? AppColors.backgroundColor
                            : AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTimeScheduleGrid() {
    final List<String> timeSlots = ['6 AM', '7 AM', '8 AM', '9 AM'];
    final List<String> dayAbbreviations = [
      'Sun',
      'Mon',
      'Tue',
      'Wed',
      'Thu',
      'Fri',
      'Sat',
    ];

    // Activity blocks data - representing which time slots have activities for each day
    final List<List<bool>> activityGrid = [
      [false, false, false, false], // Sun
      [false, false, false, false], // Mon
      [false, false, false, false], // Tue
      [false, false, true, true], // Wed - activities at 8AM and 9AM
      [false, false, true, true], // Thu - activities at 8AM and 9AM
      [false, false, true, true], // Fri - activities at 8AM and 9AM
      [false, false, true, true], // Sat - activities at 8AM and 9AM
    ];

    return SizedBox(
      height: 180.h,
      child: Column(
        children: [
          // Main grid
          Expanded(
            child: Row(
              children: [
                // Time labels column
                SizedBox(
                  width: 50.w,
                  child: Column(
                    children: timeSlots.map((time) {
                      return Expanded(
                        child: Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            time,
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),

                // Activity grid
                Expanded(
                  child: Column(
                    children: List.generate(timeSlots.length, (timeIndex) {
                      return Expanded(
                        child: Row(
                          children: List.generate(dayAbbreviations.length, (
                            dayIndex,
                          ) {
                            final bool hasActivity =
                                activityGrid[dayIndex][timeIndex];
                            return Expanded(
                              child: Container(
                                margin: EdgeInsets.all(2.w),
                                decoration: BoxDecoration(
                                  color: hasActivity
                                      ? AppColors.primaryAccent
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(4.r),
                                  border: Border.all(
                                    color: AppColors.textSecondary.withValues(
                                      alpha: 0.2,
                                    ),
                                    width: 0.5,
                                  ),
                                ),
                                child: hasActivity
                                    ? Center(
                                        child: Text(
                                          'RU',
                                          style: TextStyle(
                                            fontSize: 8.sp,
                                            fontWeight: FontWeight.w600,
                                            color: AppColors.backgroundColor,
                                          ),
                                        ),
                                      )
                                    : null,
                              ),
                            );
                          }),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 8.h),

          // Day abbreviations row
          Row(
            children: [
              SizedBox(width: 50.w), // Space for time labels
              Expanded(
                child: Row(
                  children: dayAbbreviations.map((day) {
                    return Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: TextStyle(
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityIconsSection() {
    final List<Map<String, dynamic>> activities = [
      {'icon': Icons.bed_outlined, 'label': 'Bed'},
      {'icon': Icons.nightlight_outlined, 'label': 'Sleep'},
      {'icon': Icons.restaurant_outlined, 'label': 'Food'},
      {'icon': Icons.directions_run_outlined, 'label': 'Run'},
      {'icon': Icons.work_outline, 'label': 'Work'},
    ];

    return SizedBox(
      height: 100.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: activities.map((activity) {
          return GestureDetector(
            onTap: () {
              // Handle activity selection
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.textSecondary.withValues(alpha: 0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Icon(
                    activity['icon'] as IconData,
                    color: AppColors.textSecondary,
                    size: 24.sp,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  activity['label'] as String,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCreateRoutineButton() {
    return GestureDetector(
      onTap: () {
        // Handle create routine action
      },
      child: Container(
        width: double.infinity,
        height: 56.h,
        decoration: BoxDecoration(
          color: AppColors.primaryAccent,
          borderRadius: BorderRadius.circular(28.r),
        ),
        child: Center(
          child: Text(
            'Create Routine',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.backgroundColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCheckoutRoutineSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Checkout Routine',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildRoutineCard(
                imageColor: const Color(0xFFFF9A8B),
                title: 'Beach Morning Routine',
                stats: '12,456 Steps',
                subtitle: 'Target',
                userInitials: 'JD',
                userName: 'Jhon doi',
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildRoutineCard(
                imageColor: const Color(0xFFA8E6CF),
                title: 'Running',
                stats: '25 km',
                subtitle: 'Target',
                userInitials: 'JD',
                userName: 'Jhon doi',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoutineCard({
    required Color imageColor,
    required String title,
    required String stats,
    required String subtitle,
    required String userInitials,
    required String userName,
  }) {
    return Container(
      height: 200.h,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image section
          Container(
            height: 120.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: imageColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppSpacing.radiusLg),
                topRight: Radius.circular(AppSpacing.radiusLg),
              ),
            ),
          ),

          // Content section
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    stats,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textTertiary,
                    ),
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 10.r,
                        backgroundColor: AppColors.primaryAccent,
                        child: Text(
                          userInitials,
                          style: TextStyle(
                            fontSize: 8.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.backgroundColor,
                          ),
                        ),
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        userName,
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionalCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.primaryAccent,
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Share your routine to get next month free',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.backgroundColor,
                    height: 1.3,
                  ),
                ),
                SizedBox(height: 16.h),
                GestureDetector(
                  onTap: () {
                    // Handle share action
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 10.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      'Share Now',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryAccent,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            flex: 1,
            child: Container(
              height: 80.h,
              decoration: BoxDecoration(
                color: AppColors.backgroundColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
              ),
              child: Stack(
                children: [
                  // Decorative elements
                  Positioned(
                    top: 10.h,
                    right: 10.w,
                    child: Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 15.h,
                    left: 15.w,
                    child: Container(
                      width: 6.w,
                      height: 6.w,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 25.h,
                    left: 20.w,
                    child: Container(
                      width: 4.w,
                      height: 4.w,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  // Star icon in center
                  Center(
                    child: Icon(Icons.star, color: Colors.white, size: 24.sp),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
