import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_spacing.dart';
import '../../widgets/custom_bottom_nav.dart';
import '../../widgets/profile_card.dart';
import '../../widgets/profile_menu_item.dart';
import '../../widgets/logout_button.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.gradientStart, AppColors.gradientEnd],
            stops: [0.0, 0.5431],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSpacing.md),
            child: Column(
              children: [
                SizedBox(height: AppSpacing.lg),

                // Profile Card
                const ProfileCard(
                  name: '<PERSON><PERSON><PERSON>',
                  email: 'Jhon<PERSON><PERSON>@gmail.com',
                  avatarAsset: 'assets/images/profile_avatar.png',
                ),

                SizedBox(height: AppSpacing.lg),

                // Menu Items
                Expanded(
                  child: Column(
                    children: [
                      ProfileMenuItem(icon: Icons.person_outline, onTap: () {}),
                      SizedBox(height: AppSpacing.md),

                      ProfileMenuItem(icon: Icons.share_outlined, onTap: () {}),
                      SizedBox(height: AppSpacing.md),

                      ProfileMenuItem(
                        icon: Icons.hexagon_outlined,
                        onTap: () {},
                      ),
                      SizedBox(height: AppSpacing.md),

                      ProfileMenuItem(icon: Icons.star_outline, onTap: () {}),
                      SizedBox(height: AppSpacing.md),

                      ProfileMenuItem(
                        icon: Icons.security_outlined,
                        onTap: () {},
                      ),
                      SizedBox(height: AppSpacing.md),

                      ProfileMenuItem(icon: Icons.people_outline, onTap: () {}),

                      const Spacer(),

                      // Logout Button
                      const LogoutButton(),

                      SizedBox(height: AppSpacing.lg),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: const CustomBottomNav(currentIndex: 4),
    );
  }
}
