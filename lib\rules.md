# 📐 Flutter UI Development Rules

## 🎯 Project Scope

- This project **only involves designing the UI** (no backend or logic).
- The UI must be **pixel-perfect** as per the provided design (Figma, Image, etc).

---

## 🔍 Pixel Perfect Standards

- Match **font sizes, paddings, margins, colors, shadows**, and **layouts** **exactly** as per the design.
- Use `flutter_screenutil`, `LayoutBuilder`, or `MediaQuery` for **responsive designs** if needed.
- **No approximation** — if it's `16px` in Figma, it's `16.0` in code.

---

## 🗂 Folder Structure

```bash
/lib
  /core         # Constants, themes, styles, utilities
  /widgets      # Reusable small widgets (buttons, cards, etc.)
  /screens      # Full screens, each in its own folder
  /components   # Page-specific or complex reusable components
  /assets       # Fonts, images, svgs (usually placed in root project folder)
```

---

## 🔁 Reusability Rules

- Never hardcode styles inside widgets — use constants.
- Move **any repeating widget or UI pattern** into `/widgets` or `/components`.
- Create **helper widgets** for buttons, text fields, containers, etc.
- Use `ThemeData` and `TextTheme` in `/core/theme.dart` for consistent design.

---

## 🎨 Styling and Themes

- Define all **colors**, **text styles**, **spacing**, etc. inside `/core/` as constants.
- Use a central `theme.dart` file to manage global app theming.
- Use `GoogleFonts` if the design uses a custom font.

---

## 📄 Naming Conventions

- Files: `snake_case`
- Classes: `PascalCase`
- Variables & functions: `camelCase`
- Widgets: Named by what they represent (e.g., `PrimaryButton`, `LoginCard`)

---

## ⚠️ Best Practices

- Use `const` constructors wherever possible.
- Do **not** add any business logic, API calls, or state management — **UI only**.
- Keep each widget **clean** and **simple**. Break complex layouts into smaller widgets.

---

## ✅ Completion Checklist

- [ ] Design matches 100% with provided image
- [ ] All padding, fonts, borders are accurate
- [ ] Reusable widgets/components created
- [ ] Organized folder structure used
- [ ] Theme and constants handled in `/core`
- [ ] No logic or unnecessary packages used
